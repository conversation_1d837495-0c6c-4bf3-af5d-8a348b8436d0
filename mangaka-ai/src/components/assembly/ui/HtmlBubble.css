/* Styles pour les bulles HTML pures */

.bubble-html-pure {
  /* Base styles déjà dans Tailwind */
}

/* Styles pour le cadre de sélection professionnel */

/* Styles pour les handles de redimensionnement */
.selection-handle {
  transition: all 0.2s ease;
}

.selection-handle:hover {
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

/* Styles pour l'édition TipTap */
.bubble-editor {
  /* Styles TipTap intégrés */
}

.bubble-editor .ProseMirror {
  outline: none;
  border: none;
  background: transparent;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.bubble-editor .ProseMirror p {
  margin: 0;
  padding: 0;
}

/* Styles pour le texte affiché */
.bubble-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Styles pour les différentes formes de bulles */
.bubble-html-pure.speech {
  /* Déjà dans Tailwind */
}

.bubble-html-pure.thought {
  /* <PERSON><PERSON>j<PERSON> dans Tailwind */
}

.bubble-html-pure.shout {
  /* Déj<PERSON> dans Tailwind */
}

.bubble-html-pure.whisper {
  /* Déjà dans Tailwind */
}

.bubble-html-pure.explosion {
  /* Déjà dans Tailwind */
}
