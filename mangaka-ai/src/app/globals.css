@import "tailwindcss";
@import "tw-animate-css";
@import "../styles/custom-scrollbar.css";

@custom-variant dark (&:is(.dark *));

/* MANGAKA AI Design System - Variables */
:root {
  /* Primary Colors - Rouge Manga */
  --primary-50: #fef2f2;
  --primary-100: #fee2e2;
  --primary-400: #f87171;
  --primary-500: #ef4444;
  --primary-600: #dc2626;
  --primary-900: #7f1d1d;

  /* Dark Colors - Noir Principal */
  --dark-50: #f8fafc;
  --dark-100: #f1f5f9;
  --dark-200: #e2e8f0;
  --dark-400: #94a3b8;
  --dark-500: #64748b;
  --dark-600: #475569;
  --dark-700: #334155;
  --dark-800: #1e293b;
  --dark-900: #0f172a;

  /* Accent Colors - Orange Manga */
  --accent-400: #fbbf24;
  --accent-500: #f59e0b;

  /* Semantic Colors */
  --success: #10b981;
  --error: #ef4444;
  --warning: #f59e0b;
  --info: #3b82f6;

  /* Fonts */
  --font-manga: 'Inter', system-ui, sans-serif;
  --font-display: 'Orbitron', monospace;
  --font-japanese: 'Noto Sans JP', sans-serif;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@theme {
  /* Colors */
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-900: var(--primary-900);

  --color-dark-50: var(--dark-50);
  --color-dark-100: var(--dark-100);
  --color-dark-200: var(--dark-200);
  --color-dark-400: var(--dark-400);
  --color-dark-500: var(--dark-500);
  --color-dark-600: var(--dark-600);
  --color-dark-700: var(--dark-700);
  --color-dark-800: var(--dark-800);
  --color-dark-900: var(--dark-900);

  --color-accent-400: var(--accent-400);
  --color-accent-500: var(--accent-500);

  --color-success: var(--success);
  --color-error: var(--error);
  --color-warning: var(--warning);
  --color-info: var(--info);

  /* Typography */
  --font-sans: var(--font-manga);
  --font-display: var(--font-display);
  --font-japanese: var(--font-japanese);

  /* Spacing - Base 4px system */
  --spacing-18: 4.5rem;
  --spacing-22: 5.5rem;
  --spacing-88: 22rem;
  --spacing-128: 32rem;

  /* Border Radius */
  --radius-manga: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Box Shadow */
  --shadow-manga: 0 4px 6px -1px rgba(239, 68, 68, 0.1), 0 2px 4px -1px rgba(239, 68, 68, 0.06);
  --shadow-manga-lg: 0 10px 15px -3px rgba(239, 68, 68, 0.1), 0 4px 6px -2px rgba(239, 68, 68, 0.05);

  /* Mobile Responsive Variables */
  --mobile-header-height: 3.5rem;
  --mobile-bottom-nav-height: 4rem;
  --mobile-touch-target: 2.75rem; /* 44px minimum touch target */
  --mobile-sidebar-width: 100vw;
  --tablet-sidebar-width: 20rem;

  /* Mobile Breakpoints */
  --mobile-max: 767px;
  --tablet-min: 768px;
  --desktop-min: 1024px;
}

/* Base Styles */
* {
  border-color: var(--dark-700);
}

body {
  background: var(--dark-900);
  color: var(--dark-50);
  font-family: var(--font-manga);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Hierarchy */
h1 {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--dark-50);
  font-family: var(--font-display);
  line-height: 1.2;
}

h2 {
  font-size: 1.875rem;
  font-weight: 600;
  color: var(--dark-50);
  font-family: var(--font-display);
  line-height: 1.3;
}

h3 {
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--dark-100);
  line-height: 1.4;
}

p {
  font-size: 1rem;
  color: var(--dark-200);
  line-height: 1.6;
}

/* Links */
a {
  color: var(--primary-500);
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-400);
}

/* Focus States */
*:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px var(--dark-900);
}

/* Utility Classes */
.manga-gradient {
  background: linear-gradient(to right, var(--dark-800), var(--dark-900));
}

.manga-border {
  border: 1px solid var(--dark-700);
}

.manga-shadow {
  box-shadow: var(--shadow-manga);
}

.manga-shadow-lg {
  box-shadow: var(--shadow-manga-lg);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile Responsive Utilities */
@layer utilities {
  /* Touch Target Utilities */
  .touch-target {
    min-height: var(--mobile-touch-target);
    min-width: var(--mobile-touch-target);
  }

  .touch-target-lg {
    min-height: 3.5rem; /* 56px */
    min-width: 3.5rem;
  }

  /* Mobile Layout Utilities */
  .mobile-header {
    height: var(--mobile-header-height);
  }

  .mobile-bottom-nav {
    height: var(--mobile-bottom-nav-height);
  }

  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile Sidebar Utilities */
  .mobile-sidebar {
    width: var(--mobile-sidebar-width);
  }

  .tablet-sidebar {
    width: var(--tablet-sidebar-width);
  }

  /* Mobile Animations */
  .slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  .slide-out-left {
    animation: slideOutLeft 0.3s ease-in;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .slide-down {
    animation: slideDown 0.3s ease-in;
  }
}

/* Mobile Animations Keyframes */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

/* Custom Scrollbar Styles - Style uniforme inspiré du script editor */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #6b7280 #374151;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: #374151;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
